{"cells": [{"cell_type": "code", "execution_count": 4, "id": "2b9913da", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from collections import Counter\n", "import numpy as np\n", "import json\n", "import pandas as pd\n", "import re"]}, {"cell_type": "code", "execution_count": 3, "id": "6391dfc6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Số lượng câu hỏi trong tập train: 2190\n", "S<PERSON> lượng bài viết luật trong corpus: 59636\n"]}], "source": ["def load_data():\n", "    # <PERSON><PERSON><PERSON>ữ liệu train\n", "    with open('train.json', 'r', encoding='utf-8') as f:\n", "        train_data = json.load(f)\n", "    train_df = pd.DataFrame(train_data)\n", "    \n", "    # Đ<PERSON>c dữ liệu legal corpus\n", "    with open('legal_corpus.json', 'r', encoding='utf-8') as f:\n", "        legal_corpus = json.load(f)\n", "    \n", "    # Chuyển đổi legal corpus thành DataFrame\n", "    # T<PERSON><PERSON> danh sách các bài viết từ corpus\n", "    articles = []\n", "    for doc in legal_corpus:\n", "        law_id = doc['law_id']\n", "        doc_id = doc['id']\n", "        for article in doc['content']:\n", "            articles.append({\n", "                'doc_id': doc_id,\n", "                'law_id': law_id,\n", "                'aid': article['aid'],\n", "                'content_Article': article['content_Article']\n", "            })\n", "    \n", "    articles_df = pd.DataFrame(articles)\n", "    \n", "    return train_df, articles_df\n", "\n", "train_df, articles_df = load_data()\n", "print(f\"<PERSON><PERSON> lượng câu hỏi trong tập train: {len(train_df)}\")\n", "print(f\"S<PERSON> lượng bài viết luật trong corpus: {len(articles_df)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "b3ee921c", "metadata": {}, "outputs": [], "source": ["class VietnameseTextCleaner: # https://ihateregex.io\n", "    VN_CHARS = 'áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệóòỏõọôốồổỗộơớờởỡợíìỉĩịúùủũụưứừửữựýỳỷỹỵđÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬÉÈẺẼẸÊẾỀỂỄỆÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÍÌỈĨỊÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴĐ'\n", "    \n", "    @staticmethod\n", "    def remove_html(text):\n", "        return re.sub(r'<[^>]*>', '', text)\n", "    \n", "    @staticmethod\n", "    def remove_emoji(text):\n", "        return emoji.replace_emoji(text, '')\n", "    \n", "    @staticmethod\n", "    def remove_url(text):\n", "        return re.sub(r'https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()!@:%_\\+.~#?&\\/\\/=]*)', '', text)\n", "    \n", "    @staticmethod\n", "    def remove_email(text):\n", "        return re.sub(r'[^@ \\t\\r\\n]+@[^@ \\t\\r\\n]+\\.[^@ \\t\\r\\n]+', '', text)\n", "    \n", "    @staticmethod\n", "    def remove_phone_number(text):\n", "        return re.sub(r'^[\\+]?[(]?[0-9]{3}[)]?[-\\s\\.]?[0-9]{3}[-\\s\\.]?[0-9]{4,6}$', '', text)\n", "    \n", "    @staticmethod\n", "    def remove_hashtags(text):\n", "        return re.sub(r'#\\w+', '', text)\n", "    \n", "    @staticmethod\n", "    def remove_unnecessary_characters(text):\n", "        text = re.sub(fr\"[^\\sa-zA-Z0-9{VietnameseTextCleaner.VN_CHARS}]\", ' ', text)\n", "        return re.sub(r'\\s+', ' ', text).strip() # Remove extra whitespace\n", "    \n", "    @staticmethod\n", "    def process_text(text):\n", "        text = VietnameseTextCleaner.remove_html(text)\n", "        text = VietnameseTextCleaner.remove_emoji(text)\n", "        text = VietnameseTextCleaner.remove_url(text)\n", "        text = VietnameseTextCleaner.remove_email(text)\n", "        text = VietnameseTextCleaner.remove_phone_number(text)\n", "        text = VietnameseTextCleaner.remove_hashtags(text)\n", "        return VietnameseTextCleaner.remove_unnecessary_characters(text)\n", "\n", "\n", "class VietnameseToneNormalizer:\n", "    VOWELS_TABLE = [\n", "        ['a', 'à', 'á', 'ả', 'ã', 'ạ', 'a'],\n", "        ['ă', 'ằ', 'ắ', 'ẳ', 'ẵ', 'ặ', 'aw'],\n", "        ['â', 'ầ', 'ấ', 'ẩ', 'ẫ', 'ậ', 'aa'],\n", "        ['e', 'è', 'é', 'ẻ', 'ẽ', 'ẹ', 'e' ],\n", "        ['ê', 'ề', 'ế', 'ể', 'ễ', 'ệ', 'ee'],\n", "        ['i', 'ì', 'í', 'ỉ', 'ĩ', 'ị', 'i' ],\n", "        ['o', 'ò', 'ó', 'ỏ', 'õ', 'ọ', 'o' ],\n", "        ['ô', 'ồ', 'ố', 'ổ', 'ỗ', 'ộ', 'oo'],\n", "        ['ơ', 'ờ', 'ớ', 'ở', 'ỡ', 'ợ', 'ow'],\n", "        ['u', 'ù', 'ú', 'ủ', 'ũ', 'ụ', 'u' ],\n", "        ['ư', 'ừ', 'ứ', 'ử', 'ữ', 'ự', 'uw'],\n", "        ['y', 'ỳ', 'ý', 'ỷ', 'ỹ', 'ỵ', 'y']\n", "    ]\n", "    \n", "    # VOWELS_TO_IDS = {}\n", "    # for i, row in enumerate(VOWELS_TABLE):\n", "    #     for j, char in enumerate(row[:-1]):\n", "    #         VOWELS_TO_IDS[char] = (i, j)\n", "    VOWELS_TO_IDS = {\n", "        'a': (0, 0), 'à': (0, 1), 'á': (0, 2), 'ả': (0, 3), 'ã': (0, 4), 'ạ': (0, 5), \n", "        'ă': (1, 0), 'ằ': (1, 1), 'ắ': (1, 2), 'ẳ': (1, 3), 'ẵ': (1, 4), 'ặ': (1, 5), \n", "        'â': (2, 0), 'ầ': (2, 1), 'ấ': (2, 2), 'ẩ': (2, 3), 'ẫ': (2, 4), 'ậ': (2, 5), \n", "        'e': (3, 0), 'è': (3, 1), 'é': (3, 2), 'ẻ': (3, 3), 'ẽ': (3, 4), 'ẹ': (3, 5), \n", "        'ê': (4, 0), 'ề': (4, 1), 'ế': (4, 2), 'ể': (4, 3), 'ễ': (4, 4), 'ệ': (4, 5), \n", "        'i': (5, 0), 'ì': (5, 1), 'í': (5, 2), 'ỉ': (5, 3), 'ĩ': (5, 4), 'ị': (5, 5), \n", "        'o': (6, 0), 'ò': (6, 1), 'ó': (6, 2), 'ỏ': (6, 3), 'õ': (6, 4), 'ọ': (6, 5), \n", "        'ô': (7, 0), 'ồ': (7, 1), 'ố': (7, 2), 'ổ': (7, 3), 'ỗ': (7, 4), 'ộ': (7, 5), \n", "        'ơ': (8, 0), 'ờ': (8, 1), 'ớ': (8, 2), 'ở': (8, 3), 'ỡ': (8, 4), 'ợ': (8, 5), \n", "        'u': (9, 0), 'ù': (9, 1), 'ú': (9, 2), 'ủ': (9, 3), 'ũ': (9, 4), 'ụ': (9, 5), \n", "        'ư': (10, 0), 'ừ': (10, 1), 'ứ': (10, 2), 'ử': (10, 3), 'ữ': (10, 4), 'ự': (10, 5), \n", "        'y': (11, 0), 'ỳ': (11, 1), 'ý': (11, 2), 'ỷ': (11, 3), 'ỹ': (11, 4), 'ỵ': (11, 5)\n", "    }\n", "    \n", "    VINAI_NORMALIZED_TONE = {\n", "        'òa': 'oà', 'Òa': 'Oà', 'ÒA': 'OÀ', \n", "        'óa': 'oá', 'Óa': 'O<PERSON>', 'Ó<PERSON>': 'O<PERSON>', \n", "        'ỏa': 'o<PERSON>', 'Ỏa': 'O<PERSON>', 'ỎA': 'OẢ',\n", "        'õa': 'oã', 'Õa': 'Oã', 'ÕA': 'OÃ',\n", "        'ọa': 'oạ', 'Ọa': 'Oạ', 'ỌA': 'OẠ',\n", "        'òe': 'oè', 'Òe': 'Oè', 'ÒE': 'O<PERSON>',\n", "        'óe': 'oé', 'Ó<PERSON>': 'O<PERSON>', 'Ó<PERSON>': 'O<PERSON>',\n", "        'ỏe': 'oẻ', 'Ỏe': 'Oẻ', 'ỎE': 'OẺ',\n", "        'õe': 'oẽ', 'Õe': 'Oẽ', 'ÕE': 'OẼ',\n", "        'ọe': 'oẹ', 'Ọe': 'Oẹ', 'ỌE': 'OẸ',\n", "        'ùy': 'uỳ', 'Ùy': 'Uỳ', 'ÙY': 'UỲ',\n", "        'úy': 'uý', 'Úy': 'Uý', 'ÚY': 'UÝ',\n", "        'ủy': 'uỷ', 'Ủy': 'Uỷ', 'ỦY': 'UỶ',\n", "        'ũy': 'uỹ', 'Ũy': 'Uỹ', 'ŨY': 'UỸ',\n", "        'ụy': 'uỵ', 'Ụy': 'Uỵ', 'ỤY': 'UỴ',\n", "    }\n", "\n", "\n", "    @staticmethod\n", "    def normalize_unicode(text):\n", "        char1252 = r'à|á|ả|ã|ạ|ầ|ấ|ẩ|ẫ|ậ|ằ|ắ|ẳ|ẵ|ặ|è|é|ẻ|ẽ|ẹ|ề|ế|ể|ễ|ệ|ì|í|ỉ|ĩ|ị|ò|ó|ỏ|õ|ọ|ồ|ố|ổ|ỗ|ộ|ờ|ớ|ở|ỡ|ợ|ù|ú|ủ|ũ|ụ|ừ|ứ|ử|ữ|ự|ỳ|ý|ỷ|ỹ|ỵ|À|Á|Ả|Ã|Ạ|Ầ|Ấ|Ẩ|Ẫ|Ậ|Ằ|Ắ|Ẳ|Ẵ|Ặ|È|É|Ẻ|Ẽ|Ẹ|Ề|Ế|Ể|Ễ|Ệ|Ì|Í|Ỉ|Ĩ|Ị|Ò|Ó|Ỏ|Õ|Ọ|Ồ|Ố|Ổ|Ỗ|Ộ|Ờ|Ớ|Ở|Ỡ|Ợ|Ù|Ú|Ủ|Ũ|Ụ|Ừ|Ứ|Ử|Ữ|Ự|Ỳ|Ý|Ỷ|Ỹ|Ỵ'\n", "        charutf8 = r'à|á|ả|ã|ạ|ầ|ấ|ẩ|ẫ|ậ|ằ|ắ|ẳ|ẵ|ặ|è|é|ẻ|ẽ|ẹ|ề|ế|ể|ễ|ệ|ì|í|ỉ|ĩ|ị|ò|ó|ỏ|õ|ọ|ồ|ố|ổ|ỗ|ộ|ờ|ớ|ở|ỡ|ợ|ù|ú|ủ|ũ|ụ|ừ|ứ|ử|ữ|ự|ỳ|ý|ỷ|ỹ|ỵ|À|Á|Ả|Ã|Ạ|Ầ|Ấ|Ẩ|Ẫ|Ậ|Ằ|Ắ|Ẳ|Ẵ|Ặ|È|É|Ẻ|Ẽ|Ẹ|Ề|Ế|Ể|Ễ|Ệ|Ì|Í|Ỉ|Ĩ|Ị|Ò|Ó|Ỏ|Õ|Ọ|Ồ|Ố|Ổ|Ỗ|Ộ|Ờ|Ớ|Ở|Ỡ|Ợ|Ù|Ú|Ủ|Ũ|Ụ|Ừ|Ứ|Ử|Ữ|Ự|Ỳ|Ý|Ỷ|Ỹ|Ỵ'\n", "        char_map = dict(zip(char1252.split('|'), charutf8.split('|')))\n", "        return re.sub(char1252, lambda x: char_map[x.group()], text.strip())\n", "    \n", "    \n", "    @staticmethod\n", "    def normalize_sentence_typing(text, vinai_normalization=False):\n", "        # https://github.com/VinAIResearch/BARTpho/blob/main/VietnameseToneNormalization.md\n", "        if vinai_normalization: # Just simply replace the wrong tone with the correct one defined by VinAI\n", "            for wrong, correct in VietnameseToneNormalizer.VINAI_NORMALIZED_TONE.items():\n", "                text = text.replace(wrong, correct)\n", "            return text.strip()\n", "        \n", "        # Or you can use this algorithm developed by <PERSON><PERSON><PERSON> to normalize Vietnamese typing in a sentence \n", "        words = text.strip().split()\n", "        for index, word in enumerate(words):\n", "            cw = re.sub(r'(^\\p{P}*)([p{L}.]*\\p{L}+)(\\p{P}*$)', r'\\1/\\2/\\3', word).split('/')\n", "            if len(cw) == 3: cw[1] = VietnameseToneNormalizer.normalize_word_typing(cw[1])\n", "            words[index] = ''.join(cw)\n", "        return ' '.join(words)\n", "    \n", "     \n", "    @staticmethod\n", "    def normalize_word_typing(word):\n", "        if not VietnameseToneNormalizer.is_valid_vietnamese_word(word): return word\n", "        chars, vowel_indexes = list(word), []\n", "        qu_or_gi, tonal_mark = False, 0\n", "        \n", "        for index, char in enumerate(chars):\n", "            if char not in VietnameseToneNormalizer.VOWELS_TO_IDS: continue\n", "            row, col = VietnameseToneNormalizer.VOWELS_TO_IDS[char]\n", "            if index > 0 and (row, chars[index - 1]) in [(9, 'q'), (5, 'g')]:\n", "                chars[index] = VietnameseToneNormalizer.VOWELS_TABLE[row][0]\n", "                qu_or_gi = True\n", "                \n", "            if not qu_or_gi or index != 1: vowel_indexes.append(index)\n", "            if col != 0:\n", "                tonal_mark = col\n", "                chars[index] = VietnameseToneNormalizer.VOWELS_TABLE[row][0]\n", "                \n", "        if len(vowel_indexes) < 2:\n", "            if qu_or_gi:\n", "                index = 1 if len(chars) == 2 else 2\n", "                if chars[index] in VietnameseToneNormalizer.VOWELS_TO_IDS:\n", "                    row, _ = VietnameseToneNormalizer.VOWELS_TO_IDS[chars[index]]\n", "                    chars[index] = VietnameseToneNormalizer.VOWELS_TABLE[row][tonal_mark]\n", "                else: chars[1] = VietnameseToneNormalizer.VOWELS_TABLE[5 if chars[1] == 'i' else 9][tonal_mark]\n", "                return ''.join(chars)\n", "            return word\n", "        \n", "        for index in vowel_indexes:\n", "            row, _ = VietnameseToneNormalizer.VOWELS_TO_IDS[chars[index]]\n", "            if row in [4, 8]: # ê, ơ\n", "                chars[index] = VietnameseToneNormalizer.VOWELS_TABLE[row][tonal_mark]\n", "                return ''.join(chars)\n", "            \n", "        index = vowel_indexes[0 if len(vowel_indexes) == 2 and vowel_indexes[-1] == len(chars) - 1 else 1] \n", "        row, _ = VietnameseToneNormalizer.VOWELS_TO_IDS[chars[index]]\n", "        chars[index] = VietnameseToneNormalizer.VOWELS_TABLE[row][tonal_mark]\n", "        return ''.join(chars)\n", "    \n", "    \n", "    @staticmethod\n", "    def is_valid_vietnamese_word(word):\n", "        vowel_indexes = -1 \n", "        for index, char in enumerate(word):\n", "            if char not in VietnameseToneNormalizer.VOWELS_TO_IDS: continue\n", "            if vowel_indexes in [-1, index - 1]: vowel_indexes = index\n", "            else: return False\n", "        return True\n", "    \n"]}, {"cell_type": "code", "execution_count": null, "id": "8b40a947", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON> xử lý câu hỏi...\n", "<PERSON><PERSON> xử lý nội dung bài viết luật...\n", "\n", "=== VÍ DỤ CÂU HỎI SAU KHI XỬ LÝ ===\n", "Gốc: <PERSON><PERSON><PERSON><PERSON> luật sư tôi có đăng ký kết hôn trên pháp luật nhưng nay vợ chồng bỏ nhau theo phong tục tập quán như vậy tôi có được phép kết hôn với người khác không ạ?\n", "<PERSON>u xử lý: <PERSON><PERSON><PERSON><PERSON> luật sư tôi có đăng ký kết hôn trên pháp luật nhưng nay vợ chồng bỏ nhau theo phong tục tập quán như vậy tôi có được phép kết hôn với người khác không ạ\n", "--------------------------------------------------\n", "Gốc: <PERSON> có quyền điều hành hoạt động của liên hiệp hợp tác xã?\n", "Sau xử lý: <PERSON> có quyền điều hành hoạt động của liên hiệp hợp tác xã\n", "--------------------------------------------------\n", "Gốc: <PERSON><PERSON><PERSON><PERSON> tự đăng ký hành nghề dịch vụ kế toán được quy định như thế nào?\n", "<PERSON>u xử lý: <PERSON><PERSON><PERSON><PERSON> tự đăng ký hành nghề dịch vụ kế toán đư<PERSON>c quy định như thế nào\n", "--------------------------------------------------\n", "\n", "=== VÍ DỤ BÀI VIẾT LUẬT SAU KHI XỬ LÝ ===\n", "Gốc: 1. <PERSON><PERSON><PERSON><PERSON> tư này quy định mã số, ti<PERSON><PERSON> chuẩn chuyên môn, nghi<PERSON><PERSON> vụ và xếp lương đối với các ngạch công...\n", "Sau xử lý: 1 Thông tư này quy định mã số tiêu chuẩn chuyên môn nghiệp vụ và xếp lương đối với các ngạch công ch...\n", "--------------------------------------------------\n", "Gốc: 1. <PERSON><PERSON><PERSON> soát viên cao cấp ngân hàng Mã số: 07.044 2. <PERSON><PERSON><PERSON> so<PERSON><PERSON> viên ch<PERSON>h ngân hàng Mã số: 07.045 3....\n", "<PERSON>u xử lý: 1 <PERSON><PERSON><PERSON> soát viên cao cấp ngân hàng Mã số 07 044 2 <PERSON><PERSON><PERSON> soát viên ch<PERSON>h ngân hàng Mã số 07 045 3 <PERSON><PERSON><PERSON>...\n", "--------------------------------------------------\n", "Gốc: 1. <PERSON><PERSON> bản lĩnh ch<PERSON>h trị vững vàng, kiên định với chủ ngh<PERSON><PERSON>, tư tưởng <PERSON>; nắm v...\n", "<PERSON>u xử lý: 1 <PERSON><PERSON> bản lĩnh chính trị vững vàng kiên định với chủ nghĩa <PERSON><PERSON><PERSON> tư tưởng <PERSON><PERSON> Chí <PERSON> nắm vững ch...\n", "--------------------------------------------------\n", "\n", "=== THỐNG KÊ SAU KHI XỬ LÝ ===\n", "<PERSON><PERSON> dài câu hỏi trung bình sau xử lý: 86.90 ký tự\n", "<PERSON><PERSON> dài bài viết luật trung bình sau xử lý: 1335.79 ký tự\n", "\n", "<PERSON>ã lưu dữ liệu đã xử lý vào processed_train.csv và processed_articles.csv\n"]}], "source": ["class VietnameseTextPreprocessor:\n", "    def __init__(self, extra_teencodes=None, max_correction_length=512):\n", "        self.extra_teencodes = extra_teencodes\n", "        self._build_teencodes()\n", "        \n", "        self.max_correction_length = max_correction_length\n", "        self.corrector = pipeline(\n", "            'text2text-generation', model='bmd1905/vietnamese-correction-v2', \n", "            torch_dtype='bfloat16', device_map='auto', num_workers=os.cpu_count()\n", "        )\n", "        print('bmd1905/vietnamese-correction-v2 is loaded successfully.')\n", "    \n", "    def _build_teencodes(self):\n", "        self.teencodes = {\n", "            'ok': ['okie', 'okey', 'ôkê', 'oki', 'oke', 'okay', 'okê'], \n", "            'không': ['kg', 'not', 'k', 'kh', 'kô', 'hok', 'ko', 'khong'], 'không phải': ['kp'], \n", "            'cảm ơn': ['tks', 'thks', 'thanks', 'ths', 'thank'], 'hồi đó': ['hùi đó'], 'muốn': ['mún'],\n", "            \n", "            'rất tốt': ['perfect', '❤️', '😍'], 'd<PERSON> thương': ['cute'], 'yêu': ['iu'], 'thích': ['thik'], \n", "            'tốt': [\n", "                'gud', 'good', 'gút', 'tot', 'nice',\n", "                'hehe', 'hihi', 'haha', 'hjhj', 'thick', '^_^', ':)', '=)'\n", "                '👍', '🎉', '😀', '😂', '🤗', '😙', '🙂'\n", "            ], \n", "            'bình thường': ['bt', 'bthg'], 'hàg': ['hàng'], \n", "            'không tốt':  ['lol', 'cc', 'huhu', ':(', '😔', '😓'],\n", "            'tệ': ['sad', 'por', 'poor', 'bad'], 'gi<PERSON> mạo': ['fake'], \n", "            'thôi': ['thui'],\n", "            'quá': ['wa', 'wá', 'qá'], 'được': ['đx', 'dk', 'dc', 'đk', 'đc'], \n", "            'với': ['vs'], 'gì': ['j'], 'rồi': ['r'], 'mình': ['m', 'mik'], \n", "            'thời gian': ['time'], 'giờ': ['h'], \n", "        }\n", "        if self.extra_teencodes: \n", "            for key, values in self.extra_teencodes.items():\n", "                if any(len(value.split()) > 1 for value in values):\n", "                    raise ValueError('The values for each key in extra_teencodes must be single words.')\n", "                self.teencodes.setdefault(key, []).extend(values)\n", "                \n", "        self.teencodes = {word: key for key, values in self.teencodes.items() for word in values}\n", "        teencode_url = 'https://raw.githubusercontent.com/htuann2712/ABSA-VLSP2018/refs/heads/main/teencode.txt'\n", "        response = requests.get(teencode_url)\n", "        \n", "        if response.status_code == 200:\n", "            text_data = StringIO(response.text)\n", "            for pair in text_data:\n", "                teencode, true_text = pair.split('\\t')\n", "                self.teencodes[teencode.strip()] = true_text.strip()\n", "            self.teencodes = {k: self.teencodes[k] for k in sorted(self.teencodes)}\n", "        else: print('Failed to fetch teencode.txt from', teencode_url)\n", "    \n", "    def normalize_teencodes(self, text):\n", "        words = []\n", "        for word in text.split():\n", "            words.append(self.teencodes.get(word, word))\n", "        return ' '.join(words)\n", "    \n", "    def correct_vietnamese_errors(self, texts):\n", "        # https://huggingface.co/bmd1905/vietnamese-correction-v2\n", "        predictions = self.corrector(texts, max_length=self.max_correction_length, truncation=True)\n", "        return [prediction['generated_text'] for prediction in predictions]\n", "    \n", "    def word_segment(self, text):\n", "        # Use underthesea for word segmentation\n", "        return word_tokenize(text, format='text')\n", "    \n", "    def process_text(self, text, normalize_tone=True, segment=True):\n", "        text = text.lower()\n", "        if normalize_tone:\n", "            text = VietnameseToneNormalizer.normalize_unicode(text)\n", "            text = VietnameseToneNormalizer.normalize_sentence_typing(text)\n", "        text = VietnameseTextCleaner.process_text(text)\n", "        text = self.normalize_teencodes(text)\n", "        return self.word_segment(text) if segment else text\n", "    \n", "    def process_batch(self, texts, correct_errors=True):\n", "        if correct_errors:\n", "            texts = [self.process_text(text, normalize_tone=True, segment=False) for text in texts]\n", "            texts = self.correct_vietnamese_errors(texts)\n", "            return [self.process_text(text, normalize_tone=False, segment=True) for text in texts]\n", "        return [self.process_text(text, normalize_tone=True, segment=True) for text in texts]\n", "    \n", "\n", "def preprocess_and_tokenize(text_data: pd.DataFrame, text_column: str, preprocessor, tokenizer, batch_size: int, max_length: int):\n", "    print('[INFO] Preprocessing and tokenizing text data...')\n", "    \n", "    # Preprocess the text column using batch processing\n", "    text_data[text_column] = text_data[text_column].apply(preprocessor.process_text)\n", "    \n", "    # Tokenize in batches\n", "    tokenized_batches = []\n", "    for i in tqdm(range(0, len(text_data), batch_size), desc='Tokenizing Batches'):\n", "        batch = text_data[text_column].iloc[i:i+batch_size].tolist()\n", "        tokenized_batch = tokenizer(batch, max_length=max_length, padding='max_length', truncation=True)\n", "        tokenized_batches.extend(tokenized_batch['input_ids'])\n", "    \n", "    text_data['input_ids'] = tokenized_batches\n", "    \n", "    return text_data"]}, {"cell_type": "code", "execution_count": null, "id": "d27d0dac", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}